// Parameters file for the Bicep template

using './main.bicep'

// Required parameters
param resourceGroupName = '${readEnvironmentVariable('AZURE_RESOURCE_GROUP')}'

// Optional parameters with defaults
param location = '${readEnvironmentVariable('AZURE_LOCATION', 'Australia East')}'
param environment = '${readEnvironmentVariable('ENVIRONMENT', 'dev')}'
param projectName = '${readEnvironmentVariable('PROJECT_NAME', 'scheduling-uplift')}'

// Storage account name - if not provided, will be auto-generated
param storageAccountName = '${readEnvironmentVariable('STORAGE_ACCOUNT_NAME', '')}'
