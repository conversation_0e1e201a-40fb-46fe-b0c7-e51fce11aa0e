// Main Bicep template for Static Website hosting on Azure Storage

@description('Name of the Azure Resource Group')
param resourceGroupName string

@description('Azure region for resources')
param location string = resourceGroup().location

@description('Name of the storage account (must be globally unique). If empty, will be auto-generated')
param storageAccountName string = ''

@description('Environment name (e.g., dev, staging, prod)')
param environment string = 'dev'

@description('Name of the project')
param projectName string = 'scheduling-uplift'

// Variables for automatic resource naming
var cleanProjectName = replace(projectName, '-', '')
var randomSuffix = uniqueString(resourceGroupName, projectName, environment)
var autoGeneratedStorageName = take('${cleanProjectName}${environment}${randomSuffix}', 24)
var finalStorageAccountName = empty(storageAccountName) ? autoGeneratedStorageName : storageAccountName

// Common tags for all resources
var commonTags = {
  Environment: environment
  Project: projectName
  Purpose: 'Static Website Hosting'
  ManagedBy: 'Bicep'
}

// Storage Account for Static Website Hosting
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: finalStorageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  tags: commonTags
  properties: {
    allowBlobPublicAccess: true
    supportsHttpsTrafficOnly: true
    minimumTlsVersion: 'TLS1_2'
    accessTier: 'Hot'
  }
}

// Enable static website hosting
resource staticWebsite 'Microsoft.Storage/storageAccounts/blobServices@2023-01-01' = {
  parent: storageAccount
  name: 'default'
  properties: {
    cors: {
      corsRules: [
        {
          allowedOrigins: ['*']
          allowedMethods: ['GET', 'HEAD', 'OPTIONS']
          allowedHeaders: ['*']
          exposedHeaders: ['*']
          maxAgeInSeconds: 300
        }
      ]
    }
  }
}

// Note: Static website configuration will be handled by the deployment pipeline
// after the storage account is created, using the pipeline's authentication context

// Outputs
output staticWebsiteUrl string = storageAccount.properties.primaryEndpoints.web
output storageAccountName string = storageAccount.name
output storageAccountId string = storageAccount.id
output resourceGroupName string = resourceGroup().name
output location string = location
