#!/bin/bash
# Pipeline-only deployment script

set -e

echo "🚀 Starting pipeline deployment..."

# Validate required environment variables
if [ -z "$STORAGE_ACCOUNT_NAME" ]; then
    echo "❌ Error: STORAGE_ACCOUNT_NAME environment variable is required"
    exit 1
fi

echo "✅ Using storage account: $STORAGE_ACCOUNT_NAME"

# Find the build artifacts
if [ -d "$(Pipeline.Workspace)/react-typescript-app" ]; then
    SOURCE_DIR="$(Pipeline.Workspace)/react-typescript-app"
elif [ -d "app/dist" ]; then
    SOURCE_DIR="app/dist"
else
    echo "❌ Error: Build artifacts not found"
    exit 1
fi

echo "📦 Found build artifacts in: $SOURCE_DIR"
echo "☁️  Uploading to Azure Storage..."

# Upload to Azure Storage Static Website
az storage blob upload-batch \
    --account-name "$STORAGE_ACCOUNT_NAME" \
    --auth-mode login \
    --destination '$web' \
    --source "$SOURCE_DIR" \
    --overwrite

echo "✅ Pipeline deployment complete!"
echo "🌐 Your app is available at: https://$STORAGE_ACCOUNT_NAME.z13.web.core.windows.net/"
