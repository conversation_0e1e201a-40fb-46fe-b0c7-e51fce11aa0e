# React Vite + TypeScript + Azure Static Website PoC

[![Build Status](https://banz-au.visualstudio.com/Scheduling%20Products%20Uplift/_apis/build/status%2Fscheduling-uplift-frontend?branchName=main)](https://banz-au.visualstudio.com/Scheduling%20Products%20Uplift/_build/latest?definitionId=3173&branchName=main)

This project demonstrates deploying a React Vite SPA with TypeScript to Azure Blob Storage Static Website Hosting using Bicep and Azure DevOps, with integrated Microsoft Entra ID (formerly Azure AD) authentication.

## Project Structure

```
├── app/                    # React Vite TypeScript application
├── bicep/                  # Infrastructure as Code
├── azure-pipelines.yml    # Azure DevOps pipeline
└── README.md
```

## Prerequisites

- Azure CLI configured with appropriate subscription
- Contributor access to Azure Resource Group
- Access to Azure DevOps space
- Node.js 20.18.3+ (see `.nvmrc`) - use `nvm use` or `./setup-node.sh`
- Bicep CLI installed (`az bicep install`)

## Tech Stack

- **Frontend**: React 18 + Vite + TypeScript
- **Authentication**: Microsoft Entra ID with MSAL.js 3.x
- **Hosting**: Azure Blob Storage Static Website
- **Infrastructure**: Bicep
- **CI/CD**: Azure DevOps Pipelines

## Architecture Decisions

### Why React 18 instead of React 19?

While React 19 offers the latest features, we chose **React 18.3.1** for this project because:

- **Azure MSAL Compatibility**: The `@azure/msal-react` library (v2.x) currently supports React 16-18 but not React 19 yet
- **Enterprise Stability**: React 18 is the current LTS version with excellent stability for enterprise applications
- **Production Readiness**: React 18 has been thoroughly tested in production environments and has comprehensive tooling support
- **Migration Path**: When Azure MSAL libraries add React 19 support, upgrading will be straightforward

### Why Azure Blob Storage instead of Azure Static Web Apps?

Although Azure Static Web Apps provides excellent integrated features, we chose **Azure Blob Storage Static Website** hosting because:

- **Regional Availability**: Azure Static Web Apps is only available in limited regions (`westus2`, `centralus`, `eastus2`, `westeurope`, `eastasia`)
- **Corporate Policy Compliance**: The organization's Azure Policy restricts deployments to Australia regions (`australiaeast`, `australiasoutheast`, `australiacentral`)
- **Regional Conflict**: Since Australia regions are not supported by Static Web Apps, corporate policy prevents deployment to the supported regions
- **Authentication Integration**: Microsoft Entra ID authentication works seamlessly with any hosting option via MSAL.js

**Note**: If your organization allows deployment to `eastasia` region or if Azure Static Web Apps adds Australia region support in the future, migrating to Static Web Apps would provide additional benefits like automatic HTTPS, global CDN, and integrated GitHub Actions.

## Quick Start

### 1. Set up Infrastructure

```bash
# Copy and edit parameters
cd bicep && cp main.parameters.example.json main.parameters.json
# Edit main.parameters.json with your values

# Deploy infrastructure
./deploy-bicep-local.sh
```

### 2. Deploy App

**Quick Deploy (Automatic):**

```bash
# Set up correct Node version
nvm use              # or ./setup-node.sh

# Deploy (auto-detects storage account from Bicep)
./deploy-auto.sh
```

**Manual Deploy:**

```bash
# Set up correct Node version
nvm use              # or ./setup-node.sh
cd app
npm install
npm run type-check    # TypeScript type checking
npm run build
export AZURE_STORAGE_ACCOUNT=yourstorageaccount
./deploy-local.sh
```

**First-Time Complete Deploy:**

```bash
# Configure bicep/main.parameters.json first, then:
./deploy-bicep-local.sh   # Deploys infrastructure + app in one go
```

## Development Commands

```bash
cd app
npm run dev           # Start development server
npm run build         # Build for production (includes TypeScript compilation)
npm run type-check    # Run TypeScript type checking
npm run lint          # Run ESLint with TypeScript support
npm run preview       # Preview production build
```

## Architecture

- **Frontend**: React SPA with TypeScript built with Vite
- **Type Safety**: Full TypeScript support with strict type checking
- **Hosting**: Azure Blob Storage Static Website
- **Infrastructure**: Managed with Bicep
- **Deployment**: Azure DevOps Pipelines

## TypeScript Features

- ✅ **Strict TypeScript**: Full type safety and IntelliSense
- ✅ **React Types**: Complete React component typing
- ✅ **Vite Integration**: Fast HMR with TypeScript support
- ✅ **ESLint + TypeScript**: Code quality and consistency
- ✅ **Type Checking**: Pre-build type validation
- ✅ **Microsoft Entra ID Types**: Full MSAL type safety and authentication

## Environment Variables

The following variables need to be configured in Azure DevOps:

- `AZURE_SUBSCRIPTION_ID`: Your Azure subscription ID
- `AZURE_RESOURCE_GROUP`: Target resource group name
- `AZURE_LOCATION`: Azure region (e.g., 'Australia East')
- `STORAGE_ACCOUNT_NAME`: Unique storage account name

For Azure AD authentication, also configure in your app's `.env.local`:

- `VITE_AZURE_CLIENT_ID`: Microsoft Entra ID App Registration client ID
- `VITE_AZURE_AUTHORITY`: Microsoft Entra ID authority URL
- `VITE_REDIRECT_URI`: Application redirect URI
- `VITE_POST_LOGOUT_REDIRECT_URI`: Post-logout redirect URI

## Microsoft Entra ID Authentication Setup

See [AZURE_AD_SETUP.md](./AZURE_AD_SETUP.md) for detailed instructions on:

- Creating a Microsoft Entra ID App Registration
- Configuring authentication settings
- Setting up environment variables
- Testing authentication flow
- Production deployment considerations
