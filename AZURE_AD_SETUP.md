# Microsoft Entra ID Authentication Setup Guide

This guide explains how to set up Microsoft Entra ID (formerly Azure AD) authentication for your React app hosted on Azure Blob Storage Static Website.

## Overview

Your React app now includes:

- ✅ **MSAL.js 3.x** - Microsoft Authentication Library for JavaScript
- ✅ **React Context** - Centralized authentication state management
- ✅ **TypeScript Support** - Full type safety for authentication
- ✅ **Production Ready** - Proper error handling and loading states

## Step 1: Create Microsoft Entra ID App Registration

### 1.1 In Azure Portal:

1. Navigate to **Microsoft Entra ID** → **App registrations** (formerly Azure Active Directory)
2. Click **New registration**
3. Configure:
   - **Name**: `Scheduling Uplift Frontend`
   - **Supported account types**: Choose based on your needs:
     - **Single tenant**: Only your organization
     - **Multi-tenant**: Any Microsoft Entra ID directory
   - **Redirect URI**:
     - Platform: **Single-page application (SPA)**
     - URI: `https://your-storage-account.z13.web.core.windows.net`
     - **⚠️ Important**: See Step 1.3 below for handling dynamic storage account names

### 1.2 Note Important Values:

After creation, note these values from the **Overview** page:

- **Application (client) ID**: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
- **Directory (tenant) ID**: `yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy`

### 1.3 Handling Dynamic Storage Account Names:

If your storage account name changes with each deployment (auto-generated names), you have several options:

**Option A - Use Wildcard Subdomain (Recommended for Dev):**

- Redirect URI: `https://*.z8.web.core.windows.net`
- ⚠️ **Security Note**: Only use wildcards for development environments

**Option B - Use Custom Domain (Recommended for Production):**

- Set up a custom domain (e.g., `https://scheduling.yourdomain.com`)
- Configure Azure CDN or Azure Front Door with your storage account
- Use the custom domain as your redirect URI

**Option C - Fixed Storage Account Name:**

- In your Bicep parameters file, specify a fixed storage account name
- Example: `"storageAccountName": { "value": "bsschedulingdev001" }`
- This ensures the same URL across deployments

**Option D - Update Redirect URI After Each Deployment:**

- Deploy infrastructure first to get the storage account URL
- Update the Microsoft Entra ID app registration with the actual URL
- Automate this with Azure CLI in your pipeline

## Step 2: Configure App Registration

### 2.1 Authentication Settings:

1. Go to **Authentication** in your app registration
2. Under **Single-page application**:
   - Add redirect URIs based on your chosen approach from Step 1.3:
     - **Development**: `http://localhost:5173`
     - **Production (Option A)**: `https://*.z8.web.core.windows.net` (wildcard)
     - **Production (Option B)**: `https://scheduling.yourdomain.com` (custom domain)
     - **Production (Option C)**: `https://bsschedulingdev001.z8.web.core.windows.net` (fixed name)
3. **Advanced settings**:
   - ✅ Allow public client flows: **No** (keep disabled for SPAs)
   - ✅ Enable ID tokens: **Yes**
   - ✅ Enable access tokens: **Yes**

### 2.2 API Permissions (Optional):

If you need to call Microsoft Graph API:

1. Go to **API permissions**
2. Add permissions:
   - **Microsoft Graph** → **Delegated permissions**
   - Add: `User.Read` (basic profile)
   - Click **Grant admin consent** (if you have admin rights)

## Step 3: Configure Your Application

### 3.1 Install Dependencies:

```bash
cd app
npm install
```

The following packages are already added to `package.json`:

- `@azure/msal-browser`: Core MSAL library
- `@azure/msal-react`: React integration (if using MSAL React components)

### 3.2 Environment Configuration:

1. Copy the environment template:

   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your values:

   ```env
   VITE_AZURE_CLIENT_ID=your-client-id-from-step-1.2
   VITE_AZURE_AUTHORITY=https://login.microsoftonline.com/your-tenant-id-from-step-1.2
   VITE_REDIRECT_URI=https://your-actual-storage-account.z13.web.core.windows.net
   VITE_POST_LOGOUT_REDIRECT_URI=https://your-actual-storage-account.z13.web.core.windows.net
   ```

   **💡 Tip**: If using auto-generated storage account names, deploy your infrastructure first, then update these URLs with the actual storage account name from the deployment output.

### 3.3 For Development:

Create `.env.development.local`:

```env
VITE_AZURE_CLIENT_ID=your-client-id
VITE_AZURE_AUTHORITY=https://login.microsoftonline.com/your-tenant-id
VITE_REDIRECT_URI=http://localhost:5173
VITE_POST_LOGOUT_REDIRECT_URI=http://localhost:5173
```

## Step 4: Test Authentication

### 4.1 Local Development:

```bash
cd app
npm run dev
```

Visit `http://localhost:5173` and test the "Sign in with Microsoft" button.

### 4.2 Production Testing:

1. Deploy your app:

   ```bash
   npm run build
   ./deploy-local.sh  # or use Azure DevOps pipeline
   ```

2. Visit your Azure Storage static website URL
3. Test authentication

## Step 4.5: Automating Redirect URI Updates (Optional)

If you choose **Option D** from Step 1.3, you can automate updating the Microsoft Entra ID app registration with the actual storage account URL after deployment:

### 4.5.1 Azure CLI Script:

Create a script to update the redirect URI after deployment:

```bash
#!/bin/bash
# update-redirect-uri.sh

# Get the actual storage account URL from deployment output
STORAGE_URL=$(az storage account show --name "$STORAGE_ACCOUNT_NAME" --resource-group "$AZURE_RESOURCE_GROUP" --query "primaryEndpoints.web" --output tsv | sed 's/\/$//')

# Update the Microsoft Entra ID app registration
az ad app update --id "$AZURE_CLIENT_ID" --web-redirect-uris "http://localhost:5173" "$STORAGE_URL"

echo "✅ Updated redirect URI to: $STORAGE_URL"
```

### 4.5.2 Azure DevOps Pipeline Integration:

Add this step to your `azure-pipelines.yml` after infrastructure deployment:

```yaml
- task: AzureCLI@2
  inputs:
    azureSubscription: $(AZURE_SERVICE_CONNECTION)
    scriptType: "bash"
    scriptLocation: "inlineScript"
    inlineScript: |
      # Get storage account URL
      STORAGE_URL=$(az storage account show \
        --name "$(STORAGE_ACCOUNT_NAME)" \
        --resource-group "$(AZURE_RESOURCE_GROUP)" \
        --query "primaryEndpoints.web" \
        --output tsv | sed 's/\/$//')

      # Update app registration redirect URI
      az ad app update \
        --id "$(AZURE_CLIENT_ID)" \
        --web-redirect-uris "http://localhost:5173" "$STORAGE_URL"

      echo "Updated redirect URI to: $STORAGE_URL"
  displayName: "Update Microsoft Entra ID Redirect URI"
```

**Required Pipeline Variables:**

- `AZURE_CLIENT_ID`: Your Microsoft Entra ID app registration client ID
- Service connection must have permissions to update app registrations

## Step 5: Advanced Configuration

### 5.1 Customize Authentication Scopes:

Edit `src/auth/authConfig.ts`:

```typescript
export const loginRequest = {
  scopes: [
    "User.Read", // Basic profile
    "Mail.Read", // Read emails (example)
    "Calendars.Read", // Read calendar (example)
  ],
};
```

### 5.2 Add Conditional Access Policies:

In Microsoft Entra ID, you can configure:

- **Multi-factor authentication** requirements
- **Device compliance** requirements
- **Location-based** access controls
- **Risk-based** conditional access

### 5.3 Custom Branding:

In your Microsoft Entra ID tenant:

1. Go to **Company branding**
2. Add your organization's logo and colors
3. Users will see your branding during sign-in

## Step 6: Production Considerations

### 6.1 Security Best Practices:

- ✅ Use HTTPS in production (Azure Storage provides this)
- ✅ Configure proper redirect URIs (no wildcards)
- ✅ Use single-tenant if possible for better security
- ✅ Regularly rotate client secrets (not needed for SPAs)
- ✅ Monitor sign-in logs in Microsoft Entra ID

### 6.2 Error Handling:

The app includes comprehensive error handling for:

- Authentication failures
- Network issues
- Token refresh failures
- User cancellation

### 6.3 Performance:

- Tokens are cached in session storage
- Silent token refresh is implemented
- Minimal bundle size impact

## Troubleshooting

### Common Issues:

1. **CORS Errors**:

   - Ensure redirect URIs are correctly configured
   - Check that you're using SPA platform type

2. **Login Popup Blocked**:

   - Some browsers block popups; implement redirect flow as fallback

3. **Token Refresh Failures**:

   - Check if user session has expired
   - Verify API permissions are granted

4. **Development vs Production**:

   - Use different `.env` files for different environments
   - Ensure production URLs match Microsoft Entra ID configuration
   - If using wildcard domains, ensure they're only used in development environments

5. **Dynamic Storage Account URLs**:
   - If storage account name changes between deployments, use one of the solutions from Step 1.3
   - Consider using fixed storage account names for production environments
   - Test authentication after each deployment if URLs change

### Debug Tips:

1. Enable MSAL logging in `authConfig.ts`
2. Check browser console for detailed error messages
3. Use Microsoft Entra ID sign-in logs to diagnose authentication issues
4. Test with different user accounts and browsers

## Next Steps

Once authentication is working:

- Add role-based access control (RBAC)
- Integrate with your backend APIs using acquired tokens
- Implement user profile management
- Add audit logging for security compliance
- Consider implementing refresh token rotation for enhanced security

## Support

- [MSAL.js Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications)
- [Microsoft Entra ID Documentation](https://docs.microsoft.com/en-us/azure/active-directory/)
- [Microsoft Graph API](https://docs.microsoft.com/en-us/graph/)
