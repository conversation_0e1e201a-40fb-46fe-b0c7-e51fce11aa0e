# Quick Start Guide - React + TypeScript + Azure

## Super Quick Deploy

**For first-time deployment:**

```bash
# 1. Configure Bicep
cd bicep
cp main.parameters.example.json main.parameters.json
# Edit main.parameters.json with your values

# 2. Deploy everything at once
cd ..
./deploy-bicep-local.sh
```

---

## Complete Steps to Follow

### Phase 1: Initial Setup (5 minutes)

1. **Verify Prerequisites:**

   ```bash
   # Check Azure CLI
   az --version
   az account show

   # Check Bicep CLI
   az bicep version

   # Check/Set up Node.js (20.18.3+ required, see .nvmrc)
   nvm use              # Use version from .nvmrc
   # or ./setup-node.sh # Alternative setup script
   node --version
   npm --version
   ```

2. **Configure Bicep:**

   ```bash
   cd bicep
   cp main.parameters.example.json main.parameters.json
   ```

   Edit `main.parameters.json` with your actual values:

   - Replace `your-resource-group-name` with your actual RG name
   - Replace `youruniquestorageaccount` with a unique name (e.g., `schedulingdev001`)

### Phase 2: Deploy Infrastructure (3 minutes)

```bash
cd bicep
az deployment group create \
  --resource-group your-resource-group \
  --template-file main.bicep \
  --parameters @main.parameters.json
```

**Important:** Note the `staticWebsiteUrl` output - this is your website URL!

### Phase 3: Deploy App (2 minutes)

**Option A - Automatic (Recommended):**

```bash
# Go back to project root
cd ..

# Auto-deploy (gets storage account from Bicep automatically)
./deploy-auto.sh
```

**Option B - Manual (if you prefer explicit control):**

```bash
# Go back to project root
cd ..

# Set environment variable (use the storage account name from main.parameters.json)
export AZURE_STORAGE_ACCOUNT=youruniquestorageaccount

# Deploy
./deploy-local.sh
```

**Your app is now live!** Visit the URL shown in the output.

### Phase 4: Setup Azure DevOps Pipeline (10 minutes)

1. **In Azure DevOps:**

   - Create new pipeline
   - Choose "Existing Azure Pipelines YAML file"
   - Select `azure-pipelines.yml`

2. **Create Service Connection:**

   **Why do you need this?** Azure DevOps needs secure authentication to deploy resources to your Azure subscription. The service connection creates a "Service Principal" (like a service account) that the pipeline can use to authenticate with Azure.

   - Go to Project Settings → Service connections
   - New service connection → Azure Resource Manager
   - Use Service Principal (automatic)
   - Scope to your Resource Group
   - Name it (use this name in AZURE_SERVICE_CONNECTION variable)

   **What happens behind the scenes:**

   - Azure creates a Service Principal (app registration) in your Azure AD
   - Gives it Contributor permissions to your Resource Group
   - Azure DevOps stores the credentials securely
   - Your pipeline uses these credentials to run `az` commands and Bicep

3. **Add Pipeline Variables:**

   - `AZURE_SERVICE_CONNECTION`: (create this first in Project Settings)
   - `AZURE_RESOURCE_GROUP`: your-resource-group-name
   - `STORAGE_ACCOUNT_NAME`: youruniquestorageaccount
   - `AZURE_LOCATION`: Australia East
   - `ENVIRONMENT`: dev
   - `PROJECT_NAME`: scheduling-uplift

4. **Test Pipeline:**
   - Make a small change to `app/src/App.tsx`
   - Commit and push to main branch
   - Watch the pipeline run!

## Development Commands

```bash
cd app
npm run dev           # Start development server with HMR
npm run type-check    # TypeScript type checking
npm run lint          # ESLint with TypeScript support
npm run build         # Build with TypeScript compilation
```

## Next Steps

- Add custom domain and SSL certificate
- Implement Azure CDN for global performance
- Add environment-specific deployments (dev/staging/prod)
- Integrate with Azure Application Insights for monitoring
- Add authentication with Azure AD B2C
- Set up unit tests with Jest and React Testing Library
- Add Storybook for component documentation
