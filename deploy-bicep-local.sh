#!/bin/bash
# Local deployment script for Bicep infrastructure

set -e

echo "🏗️  Deploying infrastructure with Bicep..."

# Check if we're in the right directory
if [ ! -f "bicep/main.bicep" ]; then
    echo "❌ Error: bicep/main.bicep not found. Run this script from the project root."
    exit 1
fi

# Check if parameters file exists
if [ ! -f "bicep/main.parameters.json" ]; then
    echo "⚠️  Parameters file not found. Creating from example..."
    cp bicep/main.parameters.example.json bicep/main.parameters.json
    echo "✏️  Please edit bicep/main.parameters.json with your values, then run this script again."
    exit 1
fi

# Get resource group name from parameters file
RESOURCE_GROUP=$(cat bicep/main.parameters.json | grep -A 1 '"resourceGroupName"' | grep '"value"' | cut -d'"' -f4)

if [ -z "$RESOURCE_GROUP" ] || [ "$RESOURCE_GROUP" = "your-existing-resource-group" ]; then
    echo "❌ Error: Please set a valid resourceGroupName in bicep/main.parameters.json"
    exit 1
fi

echo "📋 Deploying to resource group: $RESOURCE_GROUP"

# Deploy the Bicep template
echo "🚀 Starting Bicep deployment..."
az deployment group create \
    --resource-group "$RESOURCE_GROUP" \
    --template-file bicep/main.bicep \
    --parameters @bicep/main.parameters.json \
    --output table

echo "✅ Bicep deployment completed!"

# Get outputs
echo "📤 Getting deployment outputs..."
DEPLOYMENT_NAME=$(az deployment group list --resource-group "$RESOURCE_GROUP" --query "[0].name" -o tsv)
WEBSITE_URL=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.outputs.staticWebsiteUrl.value" -o tsv)
STORAGE_ACCOUNT=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.outputs.storageAccountName.value" -o tsv)

echo ""
echo "🎉 Deployment Summary:"
echo "📁 Resource Group: $RESOURCE_GROUP"
echo "💾 Storage Account: $STORAGE_ACCOUNT"
echo "🌐 Website URL: $WEBSITE_URL"
echo ""
echo "Next steps:"
echo "1. Run './deploy-auto.sh' to build and deploy your React app"
echo "2. Or set AZURE_STORAGE_ACCOUNT=$STORAGE_ACCOUNT and run deploy commands manually"
