# Azure DevOps Pipeline for React TypeScript App with Bicep Infrastructure

trigger:
  - main

variables:
  vmImageName: 'ubuntu-latest'
  nodeVersion: '20.x'
  # Provide default empty value for storage account name
  storageAccountName: $[coalesce(variables['STORAGE_ACCOUNT_NAME'], '')]

stages:
  - stage: Build
    displayName: 'Build React TypeScript App'
    jobs:
      - job: BuildJob
        displayName: 'Build and Package App'
        pool:
          vmImage: $(vmImageName)
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.x'
            displayName: 'Install Node.js 20.x (required for Vite 7.x)'

          - script: |
              cd app
              npm install
              npm run build
            displayName: 'Install dependencies and build React app'

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: 'app/dist'
              artifactName: 'react-typescript-app'
              publishLocation: 'Container'
            displayName: 'Publish build artifacts'

  - stage: Infrastructure
    displayName: 'Deploy Infrastructure'
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - job: DeployInfrastructure
        displayName: 'Deploy Azure Infrastructure with Bicep'
        pool:
          vmImage: $(vmImageName)
        steps:
          - checkout: self
            displayName: 'Checkout source code for Bicep templates'

          - task: AzureCLI@2
            inputs:
              azureSubscription: $(AZURE_SERVICE_CONNECTION)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                set -e
                
                echo "🏗️  Deploying infrastructure with Bicep..."
                echo "📁 Resource Group: $(AZURE_RESOURCE_GROUP)"
                echo "🌍 Location: $(AZURE_LOCATION)"
                echo "🏷️  Environment: $(ENVIRONMENT)"
                echo "📦 Project: $(PROJECT_NAME)"
                
                # Create deployment name with timestamp
                DEPLOYMENT_NAME="bicep-deploy-$(date +%Y%m%d-%H%M%S)"
                echo "🚀 Deployment Name: $DEPLOYMENT_NAME"
                
                # Handle storage account name (may be empty for auto-generation)
                STORAGE_NAME="$(storageAccountName)"
                if [ -z "$STORAGE_NAME" ]; then
                  echo "📝 Storage account name not specified - will be auto-generated"
                else
                  echo "📝 Using specified storage account name: $STORAGE_NAME"
                fi
                
                # Deploy Bicep template with parameters
                echo "🚀 Starting Bicep deployment..."
                echo "   This may take 2-5 minutes for first-time infrastructure creation"
                echo "   Creating storage account, configuring static website, setting up CORS..."
                echo ""
                
                az deployment group create \
                  --resource-group "$(AZURE_RESOURCE_GROUP)" \
                  --name "$DEPLOYMENT_NAME" \
                  --template-file bicep/main.bicep \
                  --parameters \
                    resourceGroupName="$(AZURE_RESOURCE_GROUP)" \
                    location="$(AZURE_LOCATION)" \
                    environment="$(ENVIRONMENT)" \
                    projectName="$(PROJECT_NAME)" \
                    storageAccountName="$STORAGE_NAME" \
                  --verbose \
                  --output table
                
                if [ $? -ne 0 ]; then
                  echo "❌ Bicep deployment failed"
                  exit 1
                fi
                
                echo "✅ Bicep deployment completed successfully!"
                
                # Get outputs from deployment first
                echo "📋 Retrieving deployment outputs..."
                WEBSITE_URL=$(az deployment group show \
                  --resource-group "$(AZURE_RESOURCE_GROUP)" \
                  --name "$DEPLOYMENT_NAME" \
                  --query "properties.outputs.staticWebsiteUrl.value" \
                  --output tsv)
                
                STORAGE_NAME=$(az deployment group show \
                  --resource-group "$(AZURE_RESOURCE_GROUP)" \
                  --name "$DEPLOYMENT_NAME" \
                  --query "properties.outputs.storageAccountName.value" \
                  --output tsv)
                
                if [ -z "$WEBSITE_URL" ] || [ -z "$STORAGE_NAME" ]; then
                  echo "❌ Failed to get Bicep deployment outputs"
                  echo "Website URL: $WEBSITE_URL"
                  echo "Storage Name: $STORAGE_NAME"
                  exit 1
                fi
                
                echo "📋 Bicep outputs retrieved:"
                echo "Website URL: $WEBSITE_URL"
                echo "Storage Account: $STORAGE_NAME"
                
                # Configure static website hosting 
                # Note: While Bicep can create storage accounts, static website configuration
                # requires a separate Azure CLI command because:
                # 1. ARM/Bicep templates don't support the 'staticWebsite' property on storage accounts
                # 2. This is a known limitation - static website settings are managed via the Data Plane API
                # 3. Only Control Plane operations (create/update storage account) are available in templates
                # 4. Azure CLI uses the Storage REST API directly to enable the $web container
                # 5. Uses storage account key auth (inherited from service principal context)
                echo "🌐 Configuring static website hosting..."
                az storage blob service-properties update \
                  --account-name "$STORAGE_NAME" \
                  --static-website \
                  --404-document index.html \
                  --index-document index.html \
                  --auth-mode key
                
                echo "✅ Static website hosting configured!"
                
                echo "✅ Final outputs:"
                echo "Website URL: $WEBSITE_URL"
                echo "Storage Account: $STORAGE_NAME"
                
                # Export outputs for next stage
                echo "##vso[task.setvariable variable=STATIC_WEBSITE_URL;isOutput=true]$WEBSITE_URL"
                echo "##vso[task.setvariable variable=ACTUAL_STORAGE_ACCOUNT;isOutput=true]$STORAGE_NAME"
            name: 'bicep'
            displayName: 'Deploy Infrastructure with Bicep'

  - stage: Deploy
    displayName: 'Deploy to Azure'
    dependsOn: Infrastructure
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    variables:
      staticWebsiteUrl: $[ stageDependencies.Infrastructure.DeployInfrastructure.outputs['bicep.STATIC_WEBSITE_URL'] ]
      actualStorageAccount: $[ stageDependencies.Infrastructure.DeployInfrastructure.outputs['bicep.ACTUAL_STORAGE_ACCOUNT'] ]
    jobs:
      - deployment: DeployToAzure
        displayName: 'Deploy React TypeScript App to Azure Storage'
        pool:
          vmImage: $(vmImageName)
        environment: 'production'
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  displayName: 'Checkout source code for deployment scripts'
                  
                - download: current
                  artifact: react-typescript-app
                  displayName: 'Download build artifacts'

                - task: AzureCLI@2
                  inputs:
                    azureSubscription: $(AZURE_SERVICE_CONNECTION)
                    scriptType: 'bash'
                    scriptLocation: 'inlineScript'
                    inlineScript: |
                      set -e  # Exit on any error
                      
                      echo "🚀 Starting deployment..."
                      echo "Storage Account: '$(actualStorageAccount)'"
                      echo "Website URL: '$(staticWebsiteUrl)'"
                      
                      # Validate inputs
                      if [ -z "$(actualStorageAccount)" ] || [ "$(actualStorageAccount)" = "null" ]; then
                        echo "❌ Storage account name is empty or null"
                        echo "Available variables:"
                        echo "actualStorageAccount = '$(actualStorageAccount)'"
                        exit 1
                      fi
                      
                      echo "🔄 Running in Azure DevOps pipeline..."
                      echo "✅ Using storage account from Bicep deployment: $(actualStorageAccount)"
                      
                      # Copy build artifacts to expected location for deploy script
                      echo "📦 Setting up build artifacts..."
                      if [ ! -d "$(Pipeline.Workspace)/react-typescript-app" ]; then
                        echo "❌ Build artifacts not found at $(Pipeline.Workspace)/react-typescript-app"
                        ls -la "$(Pipeline.Workspace)/"
                        exit 1
                      fi
                      
                      cp -r "$(Pipeline.Workspace)/react-typescript-app" "$(System.DefaultWorkingDirectory)/app/dist"
                      echo "📦 Using pre-built app from pipeline..."
                      
                      # Set the storage account name for the deployment script
                      export STORAGE_ACCOUNT_NAME="$(actualStorageAccount)"
                      
                      # Use the same deployment script as local development
                      if [ ! -f "deploy-auto.sh" ]; then
                        echo "❌ deploy-auto.sh not found"
                        ls -la
                        exit 1
                      fi
                      
                      chmod +x deploy-auto.sh
                      
                      echo "☁️  Uploading to Azure Storage..."
                      if ! ./deploy-auto.sh; then
                        echo "❌ Deployment script failed"
                        exit 1
                      fi
                      
                      echo "✅ Deployment completed to: $(staticWebsiteUrl)"
                      
                      # Optional: Purge CDN cache if using Azure CDN
                      # az cdn endpoint purge --content-paths "/*" --profile-name "your-cdn-profile" --name "your-endpoint" --resource-group "$(AZURE_RESOURCE_GROUP)"
                  displayName: 'Deploy to Azure Storage Static Website'
