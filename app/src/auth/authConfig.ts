// Azure AD authentication configuration using MSAL.js
import { PublicClientApplication } from "@azure/msal-browser";
import type { Configuration, LogLevel } from "@azure/msal-browser";

// Azure AD configuration
export const msalConfig: Configuration = {
  auth: {
    clientId: import.meta.env.VITE_AZURE_CLIENT_ID || "your-client-id-here", // Application (client) ID from Azure AD
    authority:
      import.meta.env.VITE_AZURE_AUTHORITY ||
      "https://login.microsoftonline.com/common", // Tenant ID or 'common' for multi-tenant
    redirectUri: import.meta.env.VITE_REDIRECT_URI || window.location.origin, // Must match the redirect URI in Azure AD
    postLogoutRedirectUri:
      import.meta.env.VITE_POST_LOGOUT_REDIRECT_URI || window.location.origin,
  },
  cache: {
    cacheLocation: "sessionStorage", // 'sessionStorage' or 'localStorage'
    storeAuthStateInCookie: false, // Set to true for IE11 compatibility
  },
  system: {
    loggerOptions: {
      loggerCallback: (
        _level: LogLevel,
        message: string,
        containsPii: boolean
      ) => {
        if (containsPii) return;
        console.log(message);
      },
    },
  },
};

// Add scopes here for your API
export const loginRequest = {
  scopes: ["User.Read"], // Basic profile information
};

// Optional: Add scopes for accessing Microsoft Graph
export const graphConfig = {
  graphMeEndpoint: "https://graph.microsoft.com/v1.0/me",
  graphUsersEndpoint: "https://graph.microsoft.com/v1.0/users",
};

// Initialize MSAL instance
export const msalInstance = new PublicClientApplication(msalConfig);

// Account selection logic helpers
export const getActiveAccount = () => {
  const accounts = msalInstance.getAllAccounts();
  if (accounts.length === 0) {
    return null;
  } else if (accounts.length === 1) {
    return accounts[0];
  } else {
    // Multiple accounts - you might want to implement account selection logic
    return accounts[0];
  }
};
