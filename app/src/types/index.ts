// Example TypeScript types for the scheduling application
export interface ScheduleItem {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  status: ScheduleStatus;
  assignedTo?: string;
  priority: Priority;
}

export type ScheduleStatus =
  | "pending"
  | "in-progress"
  | "completed"
  | "cancelled";

export type Priority = "low" | "medium" | "high" | "critical";

export interface ScheduleApiResponse {
  schedules: ScheduleItem[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export interface ScheduleFormData {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  assignedTo: string;
  priority: Priority;
}

export interface ApiError {
  message: string;
  code: number;
  details?: string;
}

// Environment configuration
export interface AppConfig {
  apiBaseUrl: string;
  environment: "development" | "staging" | "production";
  version: string;
  features: {
    enableNotifications: boolean;
    enableReporting: boolean;
    enableAdvancedFiltering: boolean;
  };
}
