# Azure AD Environment Configuration
# Copy this file to .env.local and update with your actual values

# Azure AD App Registration Configuration
VITE_AZURE_CLIENT_ID=your-azure-ad-client-id-here
VITE_AZURE_AUTHORITY=https://login.microsoftonline.com/your-tenant-id-here
VITE_REDIRECT_URI=http://localhost:5173
VITE_POST_LOGOUT_REDIRECT_URI=http://localhost:5173

# For production, update these to your actual domain:
# VITE_REDIRECT_URI=https://your-storage-account.z13.web.core.windows.net
# VITE_POST_LOGOUT_REDIRECT_URI=https://your-storage-account.z13.web.core.windows.net
