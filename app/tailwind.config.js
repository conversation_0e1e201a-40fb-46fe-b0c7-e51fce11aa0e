/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        blue: {
          primary: '#0044B4',
          dark: '#002E7C',
          light: '#1ED2E6',
          contrast: '#00B4D4',
        },
        red: {
          light: '#FF5A54',
          contrast: '#F05252',
        },
        orange: {
          light: '#FF9900',
          contrast: '#EE8700',
        },
        green: {
          light: '#00E191',
          contrast: '#0AC84E',
        },
        signal: '#E6FF00',
      },
      fontFamily: {
        sans: ['Maison <PERSON>eue', 'Arial', 'sans-serif'],
        office: ['Arial', 'sans-serif'],
      },
    },
  },
  corePlugins: {
    preflight: false,
  },
  plugins: [],
};

