# Deployment Guide

## Step-by-Step Deployment Instructions

### 1. Prerequisites Check

Ensure you have:

- [ ] Azure CLI installed and logged in (`az login`)
- [ ] Bicep CLI installed (`az bicep install`)
- [ ] Node.js 20+ installed (required for Vite 7.x)
- [ ] Contributor access to your Azure Resource Group
- [ ] Access to Azure DevOps

### 2. Local Setup

1. **<PERSON><PERSON> and setup the project:**

   ```bash
   cd /path/to/your/project
   npm install --prefix app
   ```

2. **Configure Bicep parameters:**

   ```bash
   cd bicep
   cp main.parameters.example.json main.parameters.json
   # Edit main.parameters.json with your values
   ```

3. **Set required values in main.parameters.json:**
   ```json
   {
     "parameters": {
       "resourceGroupName": {
         "value": "your-existing-resource-group"
       },
       "location": {
         "value": "Australia East"
       },
       "environment": {
         "value": "dev"
       },
       "projectName": {
         "value": "scheduling-uplift"
       },
       "storageAccountName": {
         "value": ""
       }
     }
   }
   ```

### 3. Deploy Infrastructure (Local)

```bash
./deploy-bicep-local.sh
```

Or manually:

```bash
az deployment group create \
  --resource-group your-resource-group \
  --template-file bicep/main.bicep \
  --parameters @bicep/main.parameters.json
```

Note the output `static_website_url` - this is where your app will be hosted.

### 4. Build and Deploy App (Local)

```bash
# The deployment script will automatically detect Bicep deployments
./deploy-auto.sh
```

### 5. Azure DevOps Pipeline Setup

1. **Create a new pipeline in Azure DevOps:**

   - Use the existing `azure-pipelines.yml` file
   - Connect to your repository

2. **Create Azure Service Connection:**

   - Go to Project Settings → Service connections
   - Create new Azure Resource Manager connection
   - Use Service Principal or Managed Identity
   - Scope to your resource group
   - **Note the connection name** - you'll use this in the next step

3. **Set up required variables in Azure DevOps:**

   - Go to Pipelines → Your Pipeline → Edit → Variables
   - Add these variables:

     | Variable Name              | Example Value                   | Description                      |
     | -------------------------- | ------------------------------- | -------------------------------- |
     | `AZURE_SERVICE_CONNECTION` | `azure-subscription-connection` | Use the name from step 5.2 above |
     | `AZURE_RESOURCE_GROUP`     | `rg-scheduling-dev`             | Must already exist               |
     | `STORAGE_ACCOUNT_NAME`     | `schedulingdev001`              | **See note below** ⚠️            |
     | `AZURE_LOCATION`           | `Australia East`                | Azure region                     |
     | `ENVIRONMENT`              | `dev`                           | Environment name                 |
     | `PROJECT_NAME`             | `scheduling-uplift`             | Project identifier               |

   **⚠️ Important Note about `STORAGE_ACCOUNT_NAME`:**

   **Option A - Auto-Generated Name (Recommended):**

   - Leave `STORAGE_ACCOUNT_NAME` empty in Azure DevOps
   - Bicep will automatically create: `{project}{environment}{uniquestring}` (e.g., `schedulingupliftdevabc123`)
   - **Benefits**: No naming conflicts, fully automated

   **Option B - Predictable Name:**

   - Set a unique name (e.g., `schedulingdev001`)
   - Bicep will create the storage account with this exact name
   - **Benefits**: Predictable naming, easy to reference

### 6. Verify Deployment

1. **Check the website:**

   - Visit the URL from Bicep output
   - Verify the React app loads correctly

2. **Test the pipeline:**
   - Push changes to main branch
   - Monitor the pipeline execution
   - Verify automatic deployment

## Bicep Infrastructure Benefits

### **Advantages of Bicep:**

✅ **Native Azure Integration**: First-party Microsoft tool  
✅ **Better IntelliSense**: Rich VS Code support with autocomplete  
✅ **Azure Resource Manager**: Direct compilation to ARM templates  
✅ **Simpler Syntax**: More readable than JSON ARM templates  
✅ **No State Management**: Azure Resource Manager handles state  
✅ **Built-in Validation**: Compile-time validation of Azure resources

### **Infrastructure as Code Commands:**

| Operation    | Bicep Command                                  |
| ------------ | ---------------------------------------------- |
| **Plan**     | `az deployment group what-if`                  |
| **Deploy**   | `az deployment group create`                   |
| **Destroy**  | `az group delete` or delete resources manually |
| **Validate** | `az bicep build`                               |

## Troubleshooting

### Storage Account Name Issues

- Must be 3-24 characters
- Only lowercase letters and numbers
- Must be globally unique across all Azure

### Pipeline Failures

**Common Issues and Solutions:**

1. **"Template validation failed" Error:**

   - Validate your Bicep template first:

   ```bash
   az bicep build --file bicep/main.bicep
   ```

2. **"authorization failed: registering resource provider" Error:**

   - Your service principal lacks subscription-level permissions to register resource providers
   - **Solution A**: Ask your Azure admin to grant "Contributor" role at subscription level
   - **Solution B**: Have admin pre-register providers: `Microsoft.Storage`, `Microsoft.Web`
   - **Solution C**: The pipeline now skips provider registration automatically

3. **"You do not have the required permissions" for Storage Blob:**

   - Service principal lacks Storage Blob Data permissions
   - **Solution A**: Ask Azure admin to assign "Storage Blob Data Contributor" role to your service principal
   - **Solution B**: The deployment script now automatically uses storage account key authentication in pipelines

- Check service connection permissions
- Verify all variables are set correctly
- Ensure resource group exists and you have Contributor access

### Static Website Not Loading

- Check if static website hosting is enabled on storage account
- Verify files were uploaded to `$web` container
- Check CORS settings if making API calls

## Resource Management & Cleanup

### Bicep-Managed Resources

This project creates minimal Azure resources, all properly tagged for easy identification:

**Resources Created:**

- 1 × Azure Storage Account (for static website hosting)
- 1 × CORS configuration (attached to the storage account)

**Resource Tags Applied:**

```json
{
  "Environment": "dev",
  "Project": "scheduling-uplift",
  "Purpose": "Static Website Hosting"
}
```

### Finding Your Resources

**Option 1 - Azure Portal:**

1. Go to **Resource Groups** → Your resource group
2. **Filter by tags**: `Project = scheduling-uplift`
3. All project resources will be listed

**Option 2 - Azure CLI:**

```bash
# List all resources with project tag
az resource list --tag Project=scheduling-uplift --output table

# List resources in your resource group
az resource list --resource-group rg-scheduling-dev --output table
```

**Option 3 - Bicep:**

```bash
# See what Bicep deployed
az deployment group list --resource-group rg-scheduling-dev --output table

# See deployment details
az deployment group show --resource-group rg-scheduling-dev --name deployment-name
```

### Clean Deletion Options

**Option 1 - Delete Resource Group (Recommended):**

```bash
az group delete --resource-group rg-scheduling-dev
# Review the resources, type 'yes' to confirm
```

**Option 2 - Azure Portal:**

1. Go to your Resource Group
2. Filter by `Project = scheduling-uplift`
3. Select resources → Delete

**Option 3 - Azure CLI:**

```bash
# Delete storage account specifically
az storage account delete \
  --name schedulingdev001 \
  --resource-group rg-scheduling-dev
```

### Cost Management

**Monthly Cost Estimate:**

- Azure Storage Account (LRS): ~$0.50-2.00/month
- Data transfer: ~$0.10-1.00/month (depending on traffic)
- **Total: ~$1-3/month** for typical usage

**To Monitor Costs:**

1. Azure Portal → **Cost Management + Billing**
2. Filter by Resource Group or Project tag
3. Set up budget alerts for the resource group

### Resource Naming Convention

The Bicep configuration supports **automatic environment-aware naming**:

**Automatic Naming (Recommended):**

```json
{
  "parameters": {
    "resourceGroupName": { "value": "rg-scheduling-dev" },
    "location": { "value": "Australia East" },
    "environment": { "value": "dev" },
    "projectName": { "value": "scheduling-uplift" },
    "storageAccountName": { "value": "" }
  }
}
```

**Manual Naming (If you prefer control):**

```json
{
  "parameters": {
    "resourceGroupName": { "value": "rg-scheduling-dev" },
    "storageAccountName": { "value": "schedulingdev001" },
    "location": { "value": "Australia East" }
  }
}
```

**What happens automatically:**

- Storage account name: `{projectName}{environment}{uniqueString()}` (e.g., `schedulingupliftdevabc123`)
- All resources get consistent tags: `Environment`, `Project`, `Purpose`
- No more naming conflicts - the uniqueString() ensures uniqueness

**Benefits of automatic naming:**
✅ No naming conflicts ever  
✅ Clear environment identification  
✅ Consistent patterns across all environments  
✅ Less configuration required

This makes it crystal clear which resources belong to which environment.

## Useful Commands

```bash
# Test React app locally
cd app && npm run dev

# Validate Bicep template
az bicep build --file bicep/main.bicep

# Preview changes (what-if)
az deployment group what-if \
  --resource-group your-rg \
  --template-file bicep/main.bicep \
  --parameters @bicep/main.parameters.json

# List deployments
az deployment group list --resource-group your-rg --output table

# Get deployment outputs
az deployment group show \
  --resource-group your-rg \
  --name deployment-name \
  --query "properties.outputs"

# Manual upload to storage
az storage blob upload-batch --account-name STORAGE_NAME --auth-mode login --destination '$web' --source app/dist --overwrite

# Check storage account static website status
az storage blob service-properties show --account-name STORAGE_NAME --services b --auth-mode login
```
