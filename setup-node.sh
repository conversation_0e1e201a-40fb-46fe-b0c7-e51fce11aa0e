#!/bin/bash
# Node.js setup script for the project
# This ensures the correct Node version is used regardless of shell

set -e

echo "🔍 Setting up Node.js environment..."

# Load nvm if available
if [ -f "$HOME/.nvm/nvm.sh" ]; then
    echo "📦 Loading nvm..."
    source "$HOME/.nvm/nvm.sh"
elif [ -f "$HOME/.bashrc" ]; then
    echo "📦 Loading bashrc..."
    source "$HOME/.bashrc"
fi

# Check if nvm is available
if command -v nvm &> /dev/null; then
    echo "✅ nvm found"
    
    # Use the version specified in .nvmrc
    if [ -f ".nvmrc" ]; then
        echo "📋 Using Node version from .nvmrc: $(cat .nvmrc)"
        nvm use
    else
        echo "⚠️  No .nvmrc file found, using default Node version"
    fi
    
    echo "🚀 Current Node version: $(node --version)"
    echo "📦 Current npm version: $(npm --version)"
else
    echo "❌ nvm not found. Using system Node version: $(node --version 2>/dev/null || echo 'Node not found')"
fi

echo "✅ Node.js environment setup complete!"
