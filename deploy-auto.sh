#!/bin/bash
# Enhanced deployment script that works both locally and in pipelines

set -e

echo "🚀 Starting deployment..."

# Determine if we're running in Azure DevOps pipeline
if [ -n "$STORAGE_ACCOUNT_NAME" ]; then
    # Running in pipeline - use environment variable
    echo "🔄 Running in Azure DevOps pipeline..."
    STORAGE_ACCOUNT="$STORAGE_ACCOUNT_NAME"
    echo "✅ Using storage account from pipeline: $STORAGE_ACCOUNT"
elif [ -f "bicep/main.parameters.json" ]; then
    # Running locally with Bicep - get from Azure deployment
    echo "🏠 Running locally with Bicep..."
    echo "📋 Getting storage account name from latest Bicep deployment..."
    
    # Get resource group from parameters
    RESOURCE_GROUP=$(cat bicep/main.parameters.json | grep -A 1 '"resourceGroupName"' | grep '"value"' | cut -d'"' -f4)
    
    if [ -z "$RESOURCE_GROUP" ]; then
        echo "❌ Error: Could not determine resource group from bicep/main.parameters.json"
        exit 1
    fi
    
    # Get latest deployment
    DEPLOYMENT_NAME=$(az deployment group list --resource-group "$RESOURCE_GROUP" --query "[0].name" -o tsv)
    
    if [ -z "$DEPLOYMENT_NAME" ]; then
        echo "❌ Error: No Bicep deployments found. Run './deploy-bicep-local.sh' first."
        exit 1
    fi
    
    STORAGE_ACCOUNT=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.outputs.storageAccountName.value" -o tsv)
    echo "✅ Found storage account: $STORAGE_ACCOUNT"
else
    echo "❌ Error: No infrastructure deployment found."
    echo "   Run './deploy-bicep-local.sh' first."
    echo "   Pipeline: Set STORAGE_ACCOUNT_NAME variable."
    exit 1
fi

# Build the React app (skip if already built in pipeline)
if [ -d "app/dist" ] && [ -n "$BUILD_SOURCESDIRECTORY" ]; then
    echo "📦 Using pre-built app from pipeline..."
else
    echo "📦 Building React app..."
    cd app
    npm run build
    cd ..
fi

echo "☁️  Uploading to Azure Storage..."

# For pipeline deployments, use storage account key authentication
# For local development, try login authentication first, then fall back to key
if [ -n "$STORAGE_ACCOUNT_NAME" ]; then
    # Pipeline deployment - use storage account key
    echo "🔑 Using storage account key authentication for pipeline..."
    az storage blob upload-batch \
        --account-name "$STORAGE_ACCOUNT" \
        --auth-mode key \
        --destination '$web' \
        --source "app/dist" \
        --overwrite
else
    # Local deployment - try login first, then fall back to key
    echo "🔐 Attempting login authentication..."
    if ! az storage blob upload-batch \
        --account-name "$STORAGE_ACCOUNT" \
        --auth-mode login \
        --destination '$web' \
        --source "app/dist" \
        --overwrite 2>/dev/null; then
        
        echo "⚠️  Login authentication failed, falling back to storage key..."
        az storage blob upload-batch \
            --account-name "$STORAGE_ACCOUNT" \
            --auth-mode key \
            --destination '$web' \
            --source "app/dist" \
            --overwrite
    fi
fi

# Get the website URL (different approaches for local vs pipeline)
if [ -f "bicep/main.parameters.json" ] && [ -n "$RESOURCE_GROUP" ]; then
    # Bicep local deployment
    WEBSITE_URL=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.outputs.staticWebsiteUrl.value" -o tsv)
    echo "✅ Deployment complete!"
    echo "🌐 Your app is available at: $WEBSITE_URL"
else
    # Pipeline deployment
    echo "✅ Deployment complete!"
    echo "🌐 Check pipeline output above for the exact website URL"
fi
