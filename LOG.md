# 2025-08-01

- Created the [dac-coilnesting-rg01-bslau-tst-aes-sc](https://banz-au.visualstudio.com/Scheduling%20Products%20Uplift/_settings/adminservices?resourceId=fbf722f7-e1b8-41b8-b636-f5dbd4cb4b21) “Azure Resource Manager“ _service connection_
- Created the [scheduling-uplift-frontend](https://banz-au.visualstudio.com/Scheduling%20Products%20Uplift/_git/scheduling-uplift-frontend) repo
- Created the [scheduling-uplift-frontend](https://banz-au.visualstudio.com/Scheduling%20Products%20Uplift/_build) pipeline with the following variables:
  - `AZURE_SERVICE_CONNECTION`: `dac-coilnesting-rg01-bslau-tst-aes-sc`
  - `AZURE_RESOURCE_GROUP`: `dac-coilnesting-rg01-bslau-tst-aes`
  - `AZURE_LOCATION`: `Australia East`
  - `ENVIRONMENT`: `dev`
  - `PROJECT_NAME`: `scheduling-uplift`

# 2025-08-07

- Created the [Scheduling Uplift Frontend](https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Overview/appId/8cbd7e43-c687-4ea7-8068-932ba2818c9a/objectId/008a5d0e-7e18-4204-b730-150162304a5e/isMSAApp~/false/defaultBlade/Overview/appSignInAudience/AzureADMyOrg/servicePrincipalCreated~/true) Entra ID app registration:
  - Supported account types: "Accounts in this organizational directory only (BlueScope only - Single tenant)"
  - Redirect URI: https://bsschedulingdev001.z8.web.core.windows.net
